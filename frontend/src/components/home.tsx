import Header from "@/components/header"
import Footer from "@/components/footer"
import HeroSection from "@/components/hero-section"
import FeaturesSection from "@/components/features-section"
import AboutSection from "@/components/about-section"
import ContactSection from "@/components/contact-section"

import PromosSection from "@/components/promos-section"
import { type Promo } from "@/lib/api"

interface HomeSectionProps {
    promos: Promo[];
}

export const HomeSection = ({ promos }: HomeSectionProps) => {
    return (
        <div className="relative flex min-h-screen flex-col bg-gradient-to-b from-olive-50 via-olive-100 to-olive-50">
            {/* Background elements */}
            <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
                <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]" />
                <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]" />
                <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]" />
            </div>

            {/* Grid lines */}
            <div className="pointer-events-none absolute inset-0 z-0  bg-center opacity-[0.05]" />

            {/* <Header /> */}
            <main className="relative z-10 flex-1">
                <HeroSection />
                <FeaturesSection />
                <AboutSection />
                <PromosSection promos={promos} />

                <ContactSection />
            </main>
        </div>
    )
}

