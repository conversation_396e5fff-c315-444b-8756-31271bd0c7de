import * as React from 'react';
// @ts-ignore
import ReactQuill, { type ReactQuillProps } from 'react-quill';
import 'react-quill/dist/quill.snow.css';

// Обёртка для корректной типизации ReactQuill в JSX/TSX
const QuillWrapper = React.forwardRef<ReactQuill, ReactQuillProps>((props, ref) => {
  return <ReactQuill ref={ref} {...props} />;
});

QuillWrapper.displayName = 'QuillWrapper';

export default QuillWrapper;
