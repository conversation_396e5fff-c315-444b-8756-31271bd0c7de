import * as React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { UniversalRecordEditor } from './UniversalRecordEditor';
import { getCollectionRecords, deleteRecord } from '@/lib/pocketbase-admin';
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Eye,
  Calendar,
  User,
  FileText,
  Tag,
  MessageSquare,
  HelpCircle,
  Newspaper,
  DollarSign,
  Settings,
  Zap
} from 'lucide-react';

interface CollectionManagerProps {
  collection: string;
  title: string;
  description: string;
  pbUrl: string;
}

interface Record {
  id: string;
  created: string;
  updated: string;
  [key: string]: any;
}

const getCollectionIcon = (collection: string) => {
  const icons = {
    doctors: User,
    services: FileText,
    promos: Tag,
    reviews: MessageSquare,
    faq: HelpCircle,
    news: Newspaper,
    pages: FileText,
    prices: DollarSign,
    service_categories: Settings,
  };
  return icons[collection as keyof typeof icons] || FileText;
};

const getDisplayFields = (collection: string) => {
  const fields = {
    doctors: ['name', 'position', 'specialization'],
    services: ['name', 'short_description'],
    promos: ['title', 'description', 'discount'],
    reviews: ['name', 'rating', 'text'],
    faq: ['question', 'answer'],
    news: ['title', 'date', 'is_featured'],
    pages: ['title', 'slug'],
    prices: ['name', 'price', 'service'],
    service_categories: ['name', 'description'],
  };
  return fields[collection as keyof typeof fields] || ['name', 'title'];
};

export const CollectionManager: React.FC<CollectionManagerProps> = ({
  collection,
  title,
  description,
  pbUrl
}) => {
  const [records, setRecords] = React.useState<Record[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [error, setError] = React.useState('');
  const [token, setToken] = React.useState('');
  const [quickEditModal, setQuickEditModal] = React.useState<{
    isOpen: boolean;
    recordId: string;
  }>({ isOpen: false, recordId: '' });

  const Icon = getCollectionIcon(collection);
  const displayFields = getDisplayFields(collection);

  React.useEffect(() => {
    const savedToken = localStorage.getItem('pb_token');
    if (savedToken) {
      setToken(savedToken);
    }
  }, []);

  React.useEffect(() => {
    if (token) {
      loadRecords();
    }
  }, [collection, token]);

  const loadRecords = async () => {
    setLoading(true);
    setError('');
    try {
      const result = await getCollectionRecords(collection, 1, 50, {
        sort: '-created',
      });

      setRecords(result.items || []);
    } catch (err: any) {
      console.error('Error loading records:', err);
      setError(err?.message || 'Ошибка загрузки данных');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRecord = async (id: string) => {
    if (!confirm('Вы уверены, что хотите удалить эту запись?')) return;

    try {
      await deleteRecord(collection, id);
      setRecords(records.filter(r => r.id !== id));
    } catch (err: any) {
      console.error('Error deleting record:', err);
      setError(err?.message || 'Ошибка удаления записи');
    }
  };

  const filteredRecords = records.filter(record => {
    const searchLower = searchTerm.toLowerCase();
    return displayFields.some(field => {
      const value = record[field];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(searchLower);
      }
      return false;
    });
  });

  const formatValue = (value: any, field: string) => {
    if (value === null || value === undefined) return '-';
    
    if (field === 'created' || field === 'updated' || field === 'date') {
      return new Date(value).toLocaleDateString('ru-RU');
    }
    
    if (field === 'rating' && typeof value === 'number') {
      return `${value}/5 ⭐`;
    }
    
    if (field === 'is_featured' || field === 'published') {
      return value ? '✅' : '❌';
    }
    
    if (field === 'price' && typeof value === 'number') {
      return `${value} ₽`;
    }
    
    if (typeof value === 'string' && value.length > 100) {
      return value.substring(0, 100) + '...';
    }
    
    return value.toString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-[#8BC34A] to-[#4E8C29] rounded-lg flex items-center justify-center">
                <Icon className="w-5 h-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl">{title}</CardTitle>
                <CardDescription>{description}</CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {loading ? '...' : `${records.length} записей`}
              </Badge>
              <Button 
                size="sm"
                className="bg-[#8BC34A] hover:bg-[#4E8C29]"
                onClick={() => window.open(`https://pb.stom-line.ru/_/#/collections?collection=pbc_${collection}`, '_blank')}
              >
                <Plus className="w-4 h-4 mr-1" />
                Добавить
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder={`Поиск по ${title.toLowerCase()}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Records List */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8 text-center">
              <div className="w-8 h-8 border-2 border-[#8BC34A]/30 border-t-[#8BC34A] rounded-full animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Загрузка данных...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={loadRecords} variant="outline">
                Попробовать снова
              </Button>
            </div>
          ) : filteredRecords.length === 0 ? (
            <div className="p-8 text-center">
              <Icon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">
                {searchTerm ? 'Ничего не найдено' : 'Записей пока нет'}
              </p>
              <p className="text-sm text-gray-500">
                {searchTerm ? 'Попробуйте изменить поисковый запрос' : 'Добавьте первую запись'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredRecords.map((record) => (
                <div key={record.id} className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        {displayFields.slice(0, 3).map((field) => (
                          <div key={field} className="min-w-0">
                            <p className="text-xs text-gray-500 uppercase tracking-wide mb-1">
                              {field === 'name' ? 'Название' :
                               field === 'title' ? 'Заголовок' :
                               field === 'question' ? 'Вопрос' :
                               field === 'position' ? 'Должность' :
                               field === 'rating' ? 'Рейтинг' :
                               field === 'price' ? 'Цена' :
                               field === 'date' ? 'Дата' :
                               field === 'is_featured' ? 'Рекомендуемое' :
                               field}
                            </p>
                            <p className="text-sm text-gray-900 truncate">
                              {formatValue(record[field], field)}
                            </p>
                          </div>
                        ))}
                      </div>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {formatValue(record.created, 'created')}
                        </span>
                        <span>ID: {record.id}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setQuickEditModal({ isOpen: true, recordId: record.id })}
                        className="text-[#8BC34A] hover:text-[#4E8C29] hover:bg-[#8BC34A]/10"
                      >
                        <Zap className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(`https://pb.stom-line.ru/_/#/collections?collection=pbc_${collection}&recordId=${record.id}`, '_blank')}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        onClick={() => handleDeleteRecord(record.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Universal Record Editor */}
      {quickEditModal.isOpen && quickEditModal.recordId && (
        <UniversalRecordEditor
          collection={collection}
          recordId={quickEditModal.recordId}
          mode="modal"
          showQuickFields={false}
          isOpen={quickEditModal.isOpen}
          onClose={() => {
            setQuickEditModal({ isOpen: false, recordId: '' });
            loadRecords(); // Перезагружаем данные после редактирования
          }}
          onSave={() => {
            setQuickEditModal({ isOpen: false, recordId: '' });
            loadRecords(); // Перезагружаем данные после сохранения
          }}
          pbUrl={pbUrl}
          token={token}
        />
      )}
    </div>
  );
};
