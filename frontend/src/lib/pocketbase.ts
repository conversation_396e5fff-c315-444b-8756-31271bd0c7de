import PocketBase from 'pocketbase';

// Создаем экземпляр PocketBase с URL из переменных окружения
// Проверяем, что URL существует и используем значение по умолчанию, если нет
const apiUrl = import.meta.env.PUBLIC_API_URL || 'https://pb.stom-line.ru';

// Проверяем, что URL имеет правильный формат
let baseUrl = apiUrl;
if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
  baseUrl = `https://${baseUrl}`;
}

console.log('Используемый URL для PocketBase:', baseUrl);

const pb = new PocketBase(baseUrl);

// Проверяем, есть ли учетные данные для авторизации
if (import.meta.env.PUBLIC_POCKETBASE_EMAIL && import.meta.env.PUBLIC_POCKETBASE_PASSWORD) {
  // Авторизация будет выполнена при импорте модуля
  try {
    // Используем авторизацию через коллекцию users
    pb.collection('users').authWithPassword(
      import.meta.env.PUBLIC_POCKETBASE_EMAIL,
      import.meta.env.PUBLIC_POCKETBASE_PASSWORD
    ).then(() => {
      console.log('Успешная авторизация в PocketBase');
    }).catch(error => {
      console.error('Ошибка авторизации в PocketBase:', error);
    });
  } catch (error) {
    console.error('Ошибка при попытке авторизации:', error);
  }
}

// Экспортируем экземпляр для использования в других файлах
export default pb;

// Вспомогательные функции для работы с коллекциями

/**
 * Получить список записей из коллекции
 * @param collection Название коллекции
 * @param options Опции запроса (фильтрация, сортировка и т.д.)
 */
export async function getList(collection: string, options: any = {}) {
  try {
    return await pb.collection(collection).getList(1, 50, options);
  } catch (error) {
    console.error(`Ошибка при получении списка из коллекции ${collection}:`, error);
    throw error;
  }
}

/**
 * Получить одну запись из коллекции по ID
 * @param collection Название коллекции
 * @param id ID записи
 * @param options Опции запроса
 */
export async function getOne(collection: string, id: string, options: any = {}) {
  try {
    return await pb.collection(collection).getOne(id, options);
  } catch (error) {
    console.error(`Ошибка при получении записи из коллекции ${collection}:`, error);
    throw error;
  }
}

/**
 * Создать запись в коллекции
 * @param collection Название коллекции
 * @param data Данные для создания записи
 */
export async function create(collection: string, data: any) {
  try {
    return await pb.collection(collection).create(data);
  } catch (error) {
    console.error(`Ошибка при создании записи в коллекции ${collection}:`, error);
    throw error;
  }
}

/**
 * Обновить запись в коллекции
 * @param collection Название коллекции
 * @param id ID записи
 * @param data Данные для обновления
 */
export async function update(collection: string, id: string, data: any) {
  try {
    return await pb.collection(collection).update(id, data);
  } catch (error) {
    console.error(`Ошибка при обновлении записи в коллекции ${collection}:`, error);
    throw error;
  }
}

/**
 * Удалить запись из коллекции
 * @param collection Название коллекции
 * @param id ID записи
 */
export async function remove(collection: string, id: string) {
  try {
    return await pb.collection(collection).delete(id);
  } catch (error) {
    console.error(`Ошибка при удалении записи из коллекции ${collection}:`, error);
    throw error;
  }
}

/**
 * Получить URL для файла
 * @param collectionId ID коллекции или название
 * @param recordId ID записи
 * @param filename Имя файла
 */
export function getFileUrl(collectionId: string, recordId: string, filename: string) {
  // Формируем URL вручную, чтобы избежать проблем с устаревшим API
  return `${baseUrl}/api/files/${collectionId}/${recordId}/${filename}`;
}

/**
 * Аутентификация пользователя
 * @param email Email
 * @param password Пароль
 */
export async function login(email: string, password: string) {
  try {
    return await pb.collection('users').authWithPassword(email, password);
  } catch (error) {
    console.error('Ошибка при аутентификации:', error);
    throw error;
  }
}

/**
 * Выход пользователя
 */
export function logout() {
  pb.authStore.clear();
}

/**
 * Проверка, аутентифицирован ли пользователь
 */
export function isAuthenticated() {
  return pb.authStore.isValid;
}

/**
 * Получить текущего пользователя
 */
export function getCurrentUser() {
  // Используем свойство authStore.token вместо устаревшего model
  if (pb.authStore.isValid) {
    return pb.authStore.token;
  }
  return null;
}
