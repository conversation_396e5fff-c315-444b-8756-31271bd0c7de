import pb from './pocketbase';

// Интерфейс для связанных данных
export interface RelatedRecord {
  id: string;
  name?: string;
  title?: string;
  description?: string;
  collectionId?: string;
  collectionName?: string;
  [key: string]: any;
}

// Интерфейс для специалиста из PocketBase
export interface Doctor {
  id: string;
  surname: string;
  name: string;
  patronymic?: string;
  position: string;
  photo?: string | { name?: string; filename?: string; [key: string]: any };
  experience?: string;
  short_description?: string;
  biography?: string;

  // Связи с другими коллекциями
  specializations?: string | string[];
  services?: string | string[];

  // Расширенные данные для связей
  expand?: {
    specializations?: RelatedRecord[];
    services?: RelatedRecord[];
  };
}



// Интерфейс для категории услуг
export interface ServiceCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  meta_title?: string;
  meta_description?: string;
  created?: string;
  updated?: string;
}

// Интерфейс для услуги из PocketBase
export interface Service {
  id: string;
  name: string;
  slug: string;
  category: string;
  short_description?: string;
  content?: string;
  image?: string;
  meta_title?: string;
  meta_description?: string;
  is_featured?: boolean;
  sort_order?: number;
  expand?: {
    category?: ServiceCategory;
    doctors?: Doctor[];
  };
  created?: string;
  updated?: string;
}

// Интерфейс для FAQ из PocketBase
export interface FAQ {
  id: string;
  question: string;
  answer?: string;
  category?: string;
  is_published?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

// Интерфейс для отзывов из PocketBase
export interface Review {
  id: string;
  author: string;
  date?: string;
  title?: string;
  content?: string;
  image?: string;
  is_published?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

// Интерфейс для акций из PocketBase
export interface Promo {
  id: string;
  title: string;
  subtitle?: string;
  slug: string;
  start_date?: string;
  end_date?: string;
  content?: string;
  image?: string;
  related_services?: string | string[];
  meta_title?: string;
  meta_description?: string;
  is_active?: boolean;
  is_featured?: boolean;
  sort_order?: number;
  expand?: {
    related_services?: Service[];
  };
  created?: string;
  updated?: string;
}

// Интерфейс для новости из PocketBase
export interface News {
  id: string;
  title: string;
  slug: string;
  date: string;
  content?: string;
  image?: string | { name?: string; filename?: string; [key: string]: any };
  meta_title?: string;
  meta_description?: string;
  is_featured?: boolean;
  created?: string;
  updated?: string;
}

// Интерфейс для страницы из PocketBase
export interface Page {
  id: string;
  title: string;
  slug: string;
  content?: string;
  featured_image?: string | { name?: string; filename?: string; [key: string]: any };
  gallery?: string | string[];
  parent_slug?: string;
  meta_title?: string;
  meta_description?: string;
  is_published?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

/**
 * Получить список специалистов из PocketBase
 */
export async function getSpecialists() {
  try {
    // Используем метод getFullList для получения всех записей
    const records = await pb.collection('doctors').getFullList({
      sort: 'sort_order',
      expand: 'specializations,services'
      // Убрали фильтр, чтобы получить все записи
    });

    // Для отладки выведем первую запись, чтобы посмотреть структуру данных
    if (records.length > 0) {
      console.log('Пример записи из PocketBase:', {
        id: records[0].id,
        name: records[0].name,
        surname: records[0].surname,
        position: records[0].position,
        expand: records[0].expand
      });
    }

    console.log('Получено записей из PocketBase:', records.length);

    return records as unknown as Doctor[];
  } catch (error) {
    console.error('Ошибка при получении специалистов из PocketBase:', error);
    return [];
  }
}

/**
 * Получить услугу по ID
 */
export async function getServiceById(id: string): Promise<Service | null> {
  try {
    const record = await pb.collection('services').getOne(id, {
      expand: 'category'
    });
    return record as unknown as Service;
  } catch (error) {
    console.error(`Ошибка при получении услуги с ID ${id}:`, error);
    return null;
  }
}

/**
 * Получить список категорий услуг
 */
export async function getServiceCategories() {
  try {
    const records = await pb.collection('service_categories').getFullList({
      sort: 'name'
    });

    console.log('Получено категорий услуг из PocketBase:', records.length);
    
    return records as unknown as ServiceCategory[];
  } catch (error) {
    console.error('Ошибка при получении категорий услуг из PocketBase:', error);
    return [];
  }
}

/**
 * Получить список услуг
 */
export async function getServices(filter?: string) {
  try {
    const records = await pb.collection('services').getFullList({
      sort: 'sort_order',
      expand: 'category',
      filter: filter
    });

    console.log('Получено услуг из PocketBase:', records.length);

    // Для отладки выведем первую запись, чтобы посмотреть структуру данных
    if (records.length > 0) {
      console.log('Пример услуги из PocketBase:', {
        id: records[0].id,
        name: records[0].name,
        category: records[0].category,
        expand: records[0].expand
      });
    }

    return records as unknown as Service[];
  } catch (error) {
    console.error('Ошибка при получении услуг из PocketBase:', error);
    return [];
  }
}

/**
 * Получить список часто задаваемых вопросов
 */
export async function getFAQs(filter?: string) {
  try {
    const records = await pb.collection('faq').getFullList({
      sort: 'sort_order',
      filter: filter || 'is_published = true'
    });

    console.log('Получено FAQ из PocketBase:', records.length);

    return records as unknown as FAQ[];
  } catch (error) {
    console.error('Ошибка при получении FAQ из PocketBase:', error);
    return [];
  }
}

/**
 * Получить список отзывов
 */
export async function getReviews(filter?: string) {
  try {
    const records = await pb.collection('reviews').getFullList({
      sort: 'sort_order',
      filter: filter || 'is_published = true'
    });

    console.log('Получено отзывов из PocketBase:', records.length);

    return records as unknown as Review[];
  } catch (error) {
    console.error('Ошибка при получении отзывов из PocketBase:', error);
    return [];
  }
}

/**
 * Получить список акций
 */
export async function getPromos(filter?: string) {
  try {
    const records = await pb.collection('promos').getFullList({
      sort: 'sort_order',
      expand: 'related_services',
      filter: filter || 'is_active = true'
    });

    console.log('Получено акций из PocketBase:', records.length);

    return records as unknown as Promo[];
  } catch (error) {
    console.error('Ошибка при получении акций из PocketBase:', error);
    return [];
  }
}

/**
 * Получить список новостей
 */
export async function getNews(filter?: string) {
  try {
    const records = await pb.collection('news').getFullList({
      sort: '-date',
      filter: filter
    });

    console.log('Получено новостей из PocketBase:', records.length);

    return records as unknown as News[];
  } catch (error) {
    console.error('Ошибка при получении новостей из PocketBase:', error);
    return [];
  }
}

/**
 * Получить новость по ID
 */
export async function getNewsById(id: string): Promise<News | null> {
  try {
    const record = await pb.collection('news').getOne(id);
    return record as unknown as News;
  } catch (error) {
    console.error(`Ошибка при получении новости с ID ${id}:`, error);
    return null;
  }
}

/**
 * Получить список страниц
 */
export async function getPages(filter?: string) {
  try {
    const records = await pb.collection('pages').getFullList({
      sort: 'sort_order',
      filter: filter || 'is_published = true'
    });

    console.log('Получено страниц из PocketBase:', records.length);

    return records as unknown as Page[];
  } catch (error) {
    console.error('Ошибка при получении страниц из PocketBase:', error);
    return [];
  }
}

/**
 * Получить страницу по ID
 */
export async function getPageById(id: string): Promise<Page | null> {
  try {
    const record = await pb.collection('pages').getOne(id);
    return record as unknown as Page;
  } catch (error) {
    console.error(`Ошибка при получении страницы с ID ${id}:`, error);
    return null;
  }
}

/**
 * Получить страницу по slug
 */
export async function getPageBySlug(slug: string): Promise<Page | null> {
  try {
    const record = await pb.collection('pages').getFirstListItem(`slug="${slug}"`);
    return record as unknown as Page;
  } catch (error) {
    console.error(`Ошибка при получении страницы с slug ${slug}:`, error);
    return null;
  }
}


