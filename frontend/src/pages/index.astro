---
import '../styles/global.css'
// Component Imports
import Layout from '../layouts/Layout.astro'
import { HomeSection } from '@/components/home'
import { getPromos, type Promo } from '@/lib/api'
import { getPageSEO } from '@/lib/seo-config'

// Получаем данные из PocketBase
let promos: Promo[] = [];

// Получаем акции
try {
  const response = await getPromos();
  console.log('Получено акций из PocketBase на главной странице:', response?.length || 0);

  if (response && response.length > 0) {
    promos = response;
  } else {
    console.warn('Нет данных об акциях в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении акций:', error);
}

// Получаем SEO данные для главной страницы
const seo = getPageSEO('home');
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
>
  <HomeSection promos={promos} client:load/>
</Layout>