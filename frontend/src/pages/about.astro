---
import Layout from '@/layouts/Layout.astro'
import EditButtonServer from '@/components/admin/EditButtonServer.astro'
import PocketBase from 'pocketbase'
import { getPageSEO } from '@/lib/seo-config'

const pb = new PocketBase('https://pb.stom-line.ru')

const id = 'do0q1anuy7nzxdx'

const record = await pb.collection('pages').getOne(id, {})

// Получаем SEO данные для страницы "О клинике"
const seo = getPageSEO('about');

// Используем данные из PocketBase если они есть, иначе дефолтные
const title = record?.meta_title || seo.title;
const description = record?.meta_description || seo.description;
---

<Layout
  title={title}
  description={description}
  keywords={seo.keywords}
  type={seo.type}
>
  <div class="p-3 sm:p-5 flex justify-center container mx-auto">

    <div class="my-3 text-right">
      <EditButtonServer collection="pages" id={id} />
    </div>

    <div set:html={record.content} />
  </div>
</Layout>
