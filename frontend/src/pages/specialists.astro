---
import Layout from '@/layouts/Layout.astro'
import { Specialists } from '@/components/specialists'
import { getSpecialists, type Doctor } from '@/lib/api'
import { getPageSEO } from '@/lib/seo-config'
import { isUserAuthenticated } from '@/middleware/auth'

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

// Получаем SEO данные для страницы специалистов
const seo = getPageSEO('specialists');

// Получаем данные из PocketBase
let doctors: Doctor[] = []

try {
  const response = await getSpecialists()
  console.log('Получено специалистов из PocketBase:', response?.length || 0)

  if (response && response.length > 0) {
    doctors = response
  } else {
    console.warn('Нет данных о специалистах в PocketBase')
  }
} catch (error) {
  console.error('Ошибка при получении специалистов:', error)
}
---

<Layout
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}
  type={seo.type}
>
  <Specialists specialists={doctors} isAuthenticated={isAuthenticated} client:load />
</Layout>
