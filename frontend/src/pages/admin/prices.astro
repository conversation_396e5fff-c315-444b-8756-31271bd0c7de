---
import Layout from '@/layouts/Layout.astro';
import { CollectionManager } from '@/components/admin/CollectionManager';

---

<Layout 
  title="Управление прайс-листом - Админ-панель"
  description="Управление ценами на услуги клиники"
  noindex={true}
>
  <div class="min-h-screen bg-gradient-to-br from-[#8BC34A]/10 via-white to-[#8BC34A]/5">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center gap-3">
            <a href="/admin" class="text-[#8BC34A] hover:text-[#4E8C29] transition-colors">
              ← Назад к панели
            </a>
            <span class="text-gray-300">|</span>
            <h1 class="text-xl font-bold text-gray-900">Управление прайс-листом</h1>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <CollectionManager
        collection="prices"
        title="Прайс-лист"
        description="Управление ценами на услуги клиники"
        pbUrl="https://pb.stom-line.ru"
        client:only="react"
      />
    </main>
  </div>
</Layout>
